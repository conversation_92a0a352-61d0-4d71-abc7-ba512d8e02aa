import closeIcon from '@/assets/chatpdf/btn_close.svg';
import thumbControlIcon from '@/assets/chatpdf/btn_thumbcontrol.svg';
import toBigIcon from '@/assets/chatpdf/btn_tobig.svg';
import { dispatchInUtils } from '@/utils';
import classNames from 'classnames';
import { FC, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';
import styled from './index.less';

pdfjs.GlobalWorkerOptions.workerSrc = `/pdfjs-dist/build/pdf.worker.min.mjs`;

interface Props {
  name: string;
  url: string;
  isRoute?: boolean;
}

const PdfViewer: FC<Props> = ({ name, url, isRoute = false }) => {
  const [numPages, setNumPages] = useState<number>();
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [showSidebar, setShowSidebar] = useState<boolean>(false);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  };

  const handleToggleButton = () => {
    setShowSidebar(!showSidebar);
  };

  const handlePresentationMode = () => {
    // 简单的全屏模式实现，可以根据需要调整
    setScale(scale === 1.0 ? 1.5 : 1.0);
  };

  const handleClose = () => {
    dispatchInUtils({
      type: 'pageLayout/changePageMode',
      payload: '',
    });
    dispatchInUtils({
      type: 'pdfContainer/changeUrl',
      payload: {
        url: '',
        name: '',
        size: '',
      },
    });
  };

  return (
    <div className={styled.pdfViewer}>
      <div className={classNames(styled.pdfViewerToolsBar, { [styled.isRoute]: isRoute })}>
        <div className={styled.btnThumbControl} onClick={handleToggleButton}>
          <img src={thumbControlIcon} />
        </div>
        <div className={styled.title}>{name}</div>
        <div className={styled.btnToBig} onClick={handlePresentationMode}>
          <img src={toBigIcon} />
        </div>
        {/* 产品同步去掉 */}
        {/* {isRoute && (
          <div
            className={styled.btnToBig}
            onClick={() => downloadFile(url, `${name || '文件'}.pdf`)}
          >
            <DownloadIcon />
          </div>
        )} */}
        {!isRoute && (
          <div className={styled.btnClose} onClick={handleClose}>
            <img src={closeIcon} />
          </div>
        )}
      </div>
      <div className={styled.pdfContainer}>
        <Document file={url} onLoadSuccess={onDocumentLoadSuccess} className={styled.pdfDocument}>
          {numPages && (
            <div className={styled.pdfPages}>
              {Array.from(new Array(numPages), (el, index) => (
                <Page
                  key={`page_${index + 1}`}
                  pageNumber={index + 1}
                  scale={scale}
                  className={styled.pdfPage}
                />
              ))}
            </div>
          )}
        </Document>
      </div>
    </div>
  );
};
export default PdfViewer;
