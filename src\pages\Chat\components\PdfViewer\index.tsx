import closeIcon from '@/assets/chatpdf/btn_close.svg';
import thumbControlIcon from '@/assets/chatpdf/btn_thumbcontrol.svg';
import toBigIcon from '@/assets/chatpdf/btn_tobig.svg';
import { dispatchInUtils } from '@/utils';
import classNames from 'classnames';
import { FC, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';
import styled from './index.less';

pdfjs.GlobalWorkerOptions.workerSrc = `/pdfjs-dist/build/pdf.worker.min.mjs`;

interface Props {
  name: string;
  url: string;
  isRoute?: boolean;
}

const PdfViewer: FC<Props> = ({ name, url, isRoute = false }) => {
  const [numPages, setNumPages] = useState<number>();
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [showSidebar, setShowSidebar] = useState<boolean>(false);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
  };

  const handleToggleButton = () => {
    setShowSidebar(!showSidebar);
  };

  const handlePresentationMode = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const goToPrevPage = () => {
    setPageNumber((prev) => Math.max(prev - 1, 1));
  };

  const goToNextPage = () => {
    setPageNumber((prev) => Math.min(prev + 1, numPages || 1));
  };

  const goToPage = (page: number) => {
    setPageNumber(page);
    setShowSidebar(false);
  };

  const handleClose = () => {
    dispatchInUtils({
      type: 'pageLayout/changePageMode',
      payload: '',
    });
    dispatchInUtils({
      type: 'pdfContainer/changeUrl',
      payload: {
        url: '',
        name: '',
        size: '',
      },
    });
  };

  return (
    <div className={styled.pdfViewer}>
      <div className={classNames(styled.pdfViewerToolsBar, { [styled.isRoute]: isRoute })}>
        <div className={styled.btnThumbControl} onClick={handleToggleButton}>
          <img src={thumbControlIcon} />
        </div>
        <div className={styled.title}>{name}</div>
        <div className={styled.btnToBig} onClick={handlePresentationMode}>
          <img src={toBigIcon} />
        </div>
        {/* 产品同步去掉 */}
        {/* {isRoute && (
          <div
            className={styled.btnToBig}
            onClick={() => downloadFile(url, `${name || '文件'}.pdf`)}
          >
            <DownloadIcon />
          </div>
        )} */}
        {!isRoute && (
          <div className={styled.btnClose} onClick={handleClose}>
            <img src={closeIcon} />
          </div>
        )}
      </div>
      <div className={styled.pdfContainer}>
        {/* 侧边栏缩略图 */}
        {showSidebar && numPages && (
          <div className={styled.sidebar}>
            <div className={styled.thumbnailContainer}>
              {Array.from(new Array(numPages), (_, index) => (
                <div
                  key={`thumbnail_${index + 1}`}
                  className={`${styled.thumbnail} ${
                    pageNumber === index + 1 ? styled.activeThumbnail : ''
                  }`}
                  onClick={() => goToPage(index + 1)}
                >
                  <Page pageNumber={index + 1} scale={0.2} className={styled.thumbnailPage} />
                  <div className={styled.pageNumber}>{index + 1}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 主要PDF显示区域 */}
        <div className={styled.mainContent}>
          <Document file={url} onLoadSuccess={onDocumentLoadSuccess} className={styled.pdfDocument}>
            {numPages && (
              <div className={styled.pageContainer}>
                <Page pageNumber={pageNumber} scale={scale} className={styled.pdfPage} />

                {/* 页面导航控制 */}
                {/* <div className={styled.pageNavigation}>
                  <button
                    onClick={goToPrevPage}
                    disabled={pageNumber <= 1}
                    className={styled.navButton}
                  >
                    上一页
                  </button>
                  <span className={styled.pageInfo}>
                    {pageNumber} / {numPages}
                  </span>
                  <button
                    onClick={goToNextPage}
                    disabled={pageNumber >= numPages}
                    className={styled.navButton}
                  >
                    下一页
                  </button>
                </div> */}
              </div>
            )}
          </Document>
        </div>
      </div>
    </div>
  );
};
export default PdfViewer;
